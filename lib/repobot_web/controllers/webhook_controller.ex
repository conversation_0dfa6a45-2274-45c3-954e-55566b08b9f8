defmodule RepobotWeb.WebhookController do
  use RepobotWeb, :controller

  require Logger

  alias <PERSON><PERSON>ot.Events
  alias <PERSON>obot.{Repositories}
  alias Repobot.Handlers.GitHub.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ull<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Installation<PERSON><PERSON><PERSON>}
  alias Plug.Conn
  alias <PERSON>.PubSub

  @github_event_header "x-github-event"
  @pubsub_topic "repository_events"

  plug :cache_raw_body

  def handle(conn, params) do
    with :ok <- signature_verifier().verify_signature(conn),
         result <- handle_event(get_req_header(conn, @github_event_header), params),
         :ok <- normalize_handler_result(result) do
      json(conn, %{status: "ok"})
    else
      {:error, reason} ->
        Logger.error("Webhook error: #{inspect(reason)}")
        send_resp(conn, 400, "Bad Request")
    end
  end

  # Handle repository events
  defp handle_event(
         ["repository"],
         %{"action" => "created", "repository" => repository} = _params
       ) do
    # Create the repository in the database
    case create_repository_from_webhook(repository) do
      {:ok, _created_repo} ->
        # Broadcast the repository creation event with properly formatted data
        PubSub.broadcast(
          Repobot.PubSub,
          @pubsub_topic,
          {:repository_created,
           %{
             name: repository["name"],
             full_name: repository["full_name"],
             owner: repository["owner"]["login"],
             private: repository["private"],
             description: repository["description"],
             default_branch: repository["default_branch"]
           }}
        )

        Logger.info("Repository created and saved to database: #{repository["full_name"]}")
        :ok

      {:error, reason} ->
        Logger.error("Failed to create repository in database: #{inspect(reason)}")
        # Still broadcast the event even if database creation fails
        PubSub.broadcast(
          Repobot.PubSub,
          @pubsub_topic,
          {:repository_created,
           %{
             name: repository["name"],
             full_name: repository["full_name"],
             owner: repository["owner"]["login"],
             private: repository["private"],
             description: repository["description"],
             default_branch: repository["default_branch"]
           }}
        )

        :ok
    end
  end

  # Handle push events
  defp handle_event(["push"], payload) do
    PushHandler.handle(payload)
  end

  # Handle pull request events
  defp handle_event(["pull_request"], payload) do
    PullRequestHandler.handle(payload)
  end

  # Handle installation events
  defp handle_event(["installation"], payload) do
    Logger.info("Installation event received: #{payload["action"]}")
    InstallationHandler.handle(payload)
  end

  # Handle other events with repository context
  defp handle_event([event_type], %{"repository" => %{"full_name" => full_name}} = payload)
       when not is_nil(full_name) do
    # Find the repository and organization based on the repository full name
    case Repositories.get_repository_by(full_name: full_name) do
      nil ->
        Logger.info("Repository not found in database: #{full_name}")
        :ok

      repository ->
        # Log the event with github namespace
        Events.log_github_event(
          event_type,
          payload,
          repository.organization_id,
          nil,
          repository.id
        )

        :ok
    end
  end

  # Handle any other events
  defp handle_event([event_type], _payload) do
    Logger.debug("Unhandled event type: #{event_type}")
    :ok
  end

  defp handle_event(_, _) do
    :ok
  end

  defp signature_verifier do
    Application.get_env(:repobot, :signature_verifier, Repobot.GitHub.SignatureVerifier.Default)
  end

  defp cache_raw_body(conn, _opts) do
    {:ok, body, conn} = Conn.read_body(conn)
    conn = update_in(conn.assigns[:raw_body], &[body | &1 || []])
    conn
  end

  # Normalize handler results to :ok or {:error, reason}
  defp normalize_handler_result(:ok), do: :ok
  defp normalize_handler_result({:ok, _results}), do: :ok
  defp normalize_handler_result({:error, reason}), do: {:error, reason}

  # Create a repository in the database from GitHub webhook data
  defp create_repository_from_webhook(repository_data) do
    owner_login = repository_data["owner"]["login"]

    # Find the organization for this repository owner
    case Repobot.Accounts.get_organization_by_name(owner_login) do
      nil ->
        Logger.warning("No organization found for repository owner: #{owner_login}")
        {:error, :organization_not_found}

      organization ->
        # Check if repository already exists
        case Repositories.get_repository_by(full_name: repository_data["full_name"]) do
          nil ->
            # Create new repository
            attrs = %{
              name: repository_data["name"],
              owner: owner_login,
              full_name: repository_data["full_name"],
              language: repository_data["language"],
              fork: repository_data["fork"] || false,
              private: repository_data["private"] || false,
              data: repository_data,
              organization_id: organization.id
            }

            Repositories.create_repository(attrs)

          existing_repo ->
            # Repository already exists, just return it
            {:ok, existing_repo}
        end
    end
  end
end
