defmodule Repobot.Handlers.GitHub.PushHandler do
  @moduledoc """
  Handles GitHub push events by processing commits and syncing changes to target repositories.
  """

  require Logger

  alias Repobot.{Events, Repositories, SourceFiles}

  defmodule Push do
    @moduledoc """
    Represents a GitHub push event with processed attributes for easier handling.
    """
    defstruct [
      :repository_full_name,
      :repository_id,
      :organization_id,
      :ref,
      :default_branch,
      :commits,
      :template_repository
    ]

    @type commit :: %{
            id: String.t(),
            message: String.t(),
            modified_files: [String.t()],
            added_files: [String.t()]
          }

    @type t :: %__MODULE__{
            repository_full_name: String.t(),
            repository_id: integer() | nil,
            organization_id: integer() | nil,
            ref: String.t(),
            default_branch: String.t(),
            commits: [commit()],
            template_repository: Repobot.Repositories.Repository.t() | nil
          }

    @doc """
    Creates a new Push struct from a GitHub webhook payload.
    """
    def new(payload) do
      repository_full_name = payload["repository"]["full_name"]
      repository = Repositories.get_repository_by(full_name: repository_full_name)

      commits =
        Enum.map(payload["commits"] || [], fn commit ->
          %{
            id: commit["id"],
            message: commit["message"],
            modified_files: commit["modified"] || [],
            added_files: commit["added"] || []
          }
        end)

      %__MODULE__{
        repository_full_name: repository_full_name,
        repository_id: repository && repository.id,
        organization_id: repository && repository.organization_id,
        ref: payload["ref"],
        default_branch: payload["repository"]["default_branch"],
        commits: commits,
        template_repository: nil
      }
    end

    @doc """
    Returns true if the push is to the default branch.
    """
    def to_default_branch?(%__MODULE__{ref: ref, default_branch: default_branch}) do
      String.replace_prefix(ref, "refs/heads/", "") == default_branch
    end

    @doc """
    Loads the template repository if this push is to a template repository.
    Returns {:ok, push} if successful, {:error, reason} otherwise.
    """
    def load_template_repository(%__MODULE__{repository_full_name: full_name} = push) do
      case Repositories.get_template_repository_by(full_name: full_name) do
        {:ok, template_repo} ->
          {:ok, %{push | template_repository: template_repo}}

        {:error, :not_found} ->
          {:ok, push}
      end
    end
  end

  @doc """
  Handles a GitHub push event.
  """
  def handle(payload) do
    push = Push.new(payload)

    # Log the push event
    if push.organization_id do
      Events.log_github_event("push", payload, push.organization_id, nil, push.repository_id)
    end

    with {:ok, push} <- Push.load_template_repository(push),
         true <- should_process_push?(push) do
      process_push(push)
    else
      false ->
        Logger.info("Skipping push: not to default branch or not a template repository")
        :ok

      {:error, reason} ->
        Logger.error("Error processing push: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp should_process_push?(push) do
    Push.to_default_branch?(push) && push.template_repository != nil
  end

  defp process_push(%Push{commits: commits, template_repository: template_repo}) do
    # Group files by commit to preserve commit structure
    commits
    |> Enum.reduce({%{}, []}, fn commit, {processed_files, results} ->
      modified_files = commit.modified_files ++ commit.added_files

      # Find source files that match the modified files
      source_files =
        SourceFiles.find_by_repository_and_paths(
          template_repo.id,
          modified_files,
          preload: [:repositories, :source_repository]
        )

      if Enum.empty?(source_files) do
        {processed_files, results}
      else
        process_commit(commit, source_files, template_repo, processed_files, results)
      end
    end)
    |> elem(1)
    |> case do
      [] -> :ok
      results -> {:ok, results}
    end
  end

  defp process_commit(commit, source_files, template_repo, processed_files, results) do
    github_api = Application.get_env(:repobot, :github_api)
    sync_backend = Application.get_env(:repobot, :sync_backend)

    # Create a GitHub client for the template repository
    template_client = github_api.client(template_repo.owner, template_repo.name)

    # Attempt to fetch all source file contents for this commit
    {final_processed_map, ok_files, errors} =
      fetch_source_file_contents(
        source_files,
        template_repo,
        template_client,
        commit,
        processed_files
      )

    if Enum.empty?(errors) do
      updated_files = Enum.reverse(ok_files)

      if Enum.empty?(updated_files) do
        {final_processed_map, results}
      else
        # Get target repositories (excluding the template repository)
        target_repos =
          updated_files
          |> Enum.flat_map(& &1.repositories)
          |> Enum.reject(&(&1.id == template_repo.id))
          |> Enum.uniq_by(& &1.id)

        # Sync to each target repository
        sync_results =
          Enum.map(target_repos, fn repo ->
            sync_to_target_repository(
              repo,
              updated_files,
              template_repo,
              commit,
              github_api,
              sync_backend
            )
          end)

        {final_processed_map, [{:ok, sync_results} | results]}
      end
    else
      error_result =
        {:error,
         "Skipped sync for commit #{commit.id} due to file fetch errors: #{inspect(errors)}"}

      {final_processed_map, [error_result | results]}
    end
  end

  defp fetch_source_file_contents(
         source_files,
         template_repo,
         template_client,
         commit,
         processed_files
       ) do
    Enum.reduce(source_files, {processed_files, [], []}, fn source_file,
                                                            {processed_map_acc, ok_acc, err_acc} ->
      if Map.has_key?(processed_map_acc, source_file.id) do
        {processed_map_acc, [Map.get(processed_map_acc, source_file.id) | ok_acc], err_acc}
      else
        case fetch_and_update_source_file(
               source_file,
               template_repo,
               template_client,
               commit.id
             ) do
          {:ok, updated_source_file} ->
            {Map.put(processed_map_acc, updated_source_file.id, updated_source_file),
             [updated_source_file | ok_acc], err_acc}

          {:error, reason} ->
            {processed_map_acc, ok_acc, [{source_file.id, reason} | err_acc]}
        end
      end
    end)
  end

  defp fetch_and_update_source_file(source_file, template_repo, template_client, commit_sha) do
    github_api = Application.get_env(:repobot, :github_api)

    case github_api.get_file_content(
           template_client,
           template_repo.owner,
           template_repo.name,
           source_file.target_path,
           commit_sha
         ) do
      {:ok, content, _response} ->
        SourceFiles.update_source_file(source_file, %{content: content})

      {:error, reason} ->
        {:error, "Failed to get file content: #{inspect(reason)}"}
    end
  end

  defp sync_to_target_repository(repo, files, template_repo, commit, github_api, sync_backend) do
    # Get files for this repository
    files_for_repo =
      files
      |> Enum.filter(fn source_file ->
        Enum.any?(source_file.repositories, fn r -> r.id == repo.id end)
      end)
      |> Enum.uniq_by(& &1.id)

    # Create a client for the target repository
    target_client = github_api.client(repo.owner, repo.name)

    # Sync the files
    result =
      sync_backend.sync_changes(files_for_repo, template_repo, repo, target_client,
        commit_message: commit.message
      )

    # Log the sync event
    log_sync_event(result, template_repo, repo, commit, files_for_repo)

    result
  end

  defp log_sync_event(result, template_repo, repo, commit, files) do
    sync_event_payload = %{
      template_repository_id: template_repo.id,
      target_repository_id: repo.id,
      commit_sha: commit.id,
      file_ids: Enum.map(files, & &1.id),
      result: elem(result, 0)
    }

    sync_status =
      case elem(result, 0) do
        :ok -> "success"
        :error -> "failed"
      end

    Events.log_repobot_event(
      "sync",
      sync_event_payload,
      repo.organization_id,
      nil,
      repo.id,
      sync_status
    )
  end
end
